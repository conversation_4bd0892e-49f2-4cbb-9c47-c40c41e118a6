# Dy-Flow v3 Default Configuration
# 多鏈自動做市 + 動態避險系統

# Application Settings
app:
  name: "dyflow-v3"
  version: "3.0.0"
  description: "24/7 Multi-Chain LP Manager with Dynamic Hedging"
  health_check_port: 8080
  log_level: "INFO"

# v3 多鏈支援配置
chains:
  bsc:
    name: "Binance Smart Chain"
    chain_id: 56
    native_token: "BNB"
    rpc_url: "${BSC_RPC_URL}"
    explorer: "https://bscscan.com"
    gas_price_gwei: 5
    dex: "pancakeswap_v3"
    contracts:
      router_v3: "0x13f4EA83D0bd40E75C8222255bc855a974568Dd4"
      factory_v3: "0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865"
      quoter: "0xB048Bbc1Ee6b733FFfCFb9e9CeF7375518e25997"
      
  sol:
    name: "Solana Mainnet"
    cluster: "mainnet-beta"
    native_token: "SOL"
    rpc_url: "${SOLANA_RPC_URL}"
    explorer: "https://solscan.io"
    dex: "meteora_dlmm"
    api_endpoints:
      dlmm_pools: "https://dlmm-api.meteora.ag/v2/pools/dlmm"
      jupiter_quote: "https://quote-api.jup.ag/v6"
    program_ids:
      meteora_dlmm: "LBUZKhRxPF3XUpBCjp4YzTKgLccjZhTSDM9YuVaPwxo"

# v3 三大策略配置
strategy_defaults:
  # S-1: Delta-Neutral LP (藍籌對沖)
  delta_neutral_lp:
    description: "LP + Perp 空單對沖，賺手續費"
    enter_score_threshold: 0.60
    exit_score_threshold: 0.30
    max_pools: 5
    min_tvl_usd: 1000000  # 100萬美金
    hedge_ratio: 0.80     # 80% 對沖
    rebalance_threshold: 0.80  # 價格偏離 80% 時再平衡
    target_tokens: ["BTCB", "ETH", "BNB"]
    
  # S-2: Ladder Single-Sided LP (Meme 火箭)
  ladder_single_sided:
    description: "多層樓梯單邊 LP，捕捉 10× 機會"
    enter_score_threshold: 0.70
    exit_score_threshold: 0.40
    max_pools: 3
    min_tvl_usd: 100000   # 10萬美金
    floor_pct: 0.25       # 25% 底倉
    step_pct: 0.35        # 35% 階梯
    layers: 4             # 4層樓梯
    trailing_stop_pct: 0.15  # 15% trailing stop
    
  # S-3: Passive High-TVL (穩定池躺平)
  passive_high_tvl:
    description: "大池被動持倉，穩定年化 5-10%"
    enter_score_threshold: 0.50
    exit_score_threshold: 0.25
    max_pools: 2
    min_tvl_usd: 10000000  # 1000萬美金
    stable_pairs: ["USDT-USDC", "DAI-BUSD", "USDC-SOL"]
    rebalance_frequency: 86400  # 24小時
    target_apr: 0.08      # 8% 目標年化

# v3 風險控制參數
risk:
  # ATR + kDrop 動態風險計算
  k_drop: 2.5              # kDrop 倍數
  min_drop_pct: 0.06       # 最小跌幅閾值 (6%)
  atr_window: 14           # ATR 計算窗口
  
  # TVL 風險監控
  tvl_drop_threshold: 0.40  # TVL 30分鐘內跌幅 > 40% 觸發
  tvl_monitoring_window: 1800  # 30分鐘監控窗口
  
  # 對沖失效檢測
  hedge_failure_threshold: 3   # 連續失敗 ≥ 3次觸發
  max_delta_threshold: 0.30    # delta ≥ 30% 觸發警報
  
  # 緊急退出條件
  emergency_exit:
    price_drop_1h: 0.15      # 1小時內跌幅 > 15%
    funding_rate_spike: 2.0   # 資金費率異常 > 200%
    gas_price_spike: 100      # Gas 價格異常 > 100 gwei

# API 接口配置
api_endpoints:
  # 價格數據源
  price_feeds:
    binance: "https://api.binance.com/api/v3"
    coingecko: "https://api.coingecko.com/api/v3"
    
  # DEX 數據源
  dex_apis:
    pancakeswap_v3: "https://gateway.thegraph.graph.com/api/subgraphs/id/Hv1GncLY5docZoGtXjo4kwbTvxm3MAhVZqBZE4sUT9eZ"
    pancakeswap_v3_api_key: "9731921233db132a98c2325878e6c153"
    meteora_dlmm: "https://dammv2-api.meteora.ag"
    
  # 永續合約
  perp_exchanges:
    binance_futures: "https://fapi.binance.com"
    okx_futures: "https://www.okx.com/api/v5"

# Perpetual Exchanges
exchanges:
  binance:
    name: "Binance Futures"
    api_url: "https://fapi.binance.com"
    testnet_url: "https://testnet.binancefuture.com"
    max_leverage: 20
    
  okx:
    name: "OKX Futures"
    api_url: "https://www.okx.com"
    testnet_url: "https://www.okx.com"
    max_leverage: 25
    
  bybit:
    name: "Bybit Futures"
    api_url: "https://api.bybit.com"
    testnet_url: "https://api-testnet.bybit.com"
    max_leverage: 25

# LLM Configuration
llm:
  primary_model: "qwen3:7b"
  fallback_model: "gpt-4o"
  base_url: "http://localhost:11434"
  temperature: 0.2
  max_tokens: 2000
  timeout: 60
  
# Database Configuration
database:
  provider: "supabase"
  url: "sqlite+aiosqlite:///data/state/dyflow.db" # Use aiosqlite for async SQLite
  connection_pool_size: 10
  query_timeout: 30

# Agent Configuration
agents:
  pool_hunter:
    enabled: true
    schedule: "*/5 * * * *"  # Every 5 minutes
    timeout: 120
    retry_attempts: 3
    
  scorer:
    enabled: true
    timeout: 90
    retry_attempts: 2
    
  risk_sentinel:
    enabled: true
    schedule: "*/1 * * * *"  # Every minute
    timeout: 30
    retry_attempts: 1
    
  planner:
    enabled: true
    llm_model: "qwen3:7b"
    timeout: 180
    retry_attempts: 2
    
  executor:
    enabled: true
    timeout: 300
    retry_attempts: 1
    
  auditor:
    enabled: true
    schedule: "0 */6 * * *"  # Every 6 hours
    timeout: 120
    retry_attempts: 2
    
  critic_llm:
    enabled: true
    schedule: "0 2 * * *"   # Daily at 2 AM
    llm_model: "qwen3:7b"
    timeout: 300
    retry_attempts: 2
    
  cli_reporter:
    enabled: true
    schedule: "*/10 * * * *"  # Every 10 seconds
    timeout: 15
    retry_attempts: 1

# Monitoring & Alerts
monitoring:
  enable_metrics: true
  health_check_interval: 30
  performance_tracking: true
  cli_update_interval: 10
  
  # Telegram Alerts (optional)
  telegram:
    enabled: "${ENABLE_NOTIFICATIONS:-false}"
    bot_token: "${TELEGRAM_BOT_TOKEN}"
    chat_id: "${TELEGRAM_CHAT_ID}"
    alert_levels: ["HIGH", "CRITICAL"]

# Logging Configuration
logging:
  level: "INFO"
  format: "json"
  file: "data/logs/dyflow.log"
  max_size: "100MB"
  backup_count: 7
  
# Security Settings
security:
  rate_limiting:
    enabled: true
    requests_per_minute: 60
    
  encryption:
    enabled: true
    algorithm: "AES-256"

# Performance Optimization
performance:
  cache:
    enabled: true
    ttl: 300  # 5 minutes
    max_size: 1000
    
  batch_processing:
    enabled: true
    batch_size: 100
    
  async_execution:
    enabled: true
    max_workers: 10

# Development Settings
development:
  debug_mode: true
  test_mode: true
  dry_run: true  # Set to true for testing without real transactions
  use_real_data: true  # Use real API data for monitoring
  simulate_trading: true  # Simulate trading execution
  
# Backup Configuration
backup:
  enabled: true
  interval: "0 3 * * *"  # Daily at 3 AM
  retention_days: 30
  location: "data/backups"