#!/usr/bin/env python3
"""
DyFlow 统一启动器
24/7自动化单边LP策略系统 - 统一入口点
Framework: Agno | Chains: BSC (PancakeSwap v3) & Solana (Meteora DLMM v2)
"""

import asyncio
import signal
import sys
import os
import subprocess
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime
import structlog
import argparse
import json

# 添加src到路径
sys.path.insert(0, 'src')

# DyFlow imports (延迟导入以避免初始化错误)
# from src.supervisor import DyFlowSupervisor, SupervisorConfig
# from src.utils.config import Config
# from src.utils.helpers import setup_logging
# from src.utils.exceptions import DyFlowException

# 设置日志
logger = structlog.get_logger(__name__)

class DyFlowLauncher:
    """DyFlow 统一启动器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.config_path = self.project_root / "config" / "config.yaml"
        self.is_running = False
        self.processes = {}
        
    def print_banner(self):
        """打印启动横幅"""
        print("""
╔══════════════════════════════════════════════════════════════╗
║                        DyFlow v3.1                          ║
║              24/7 自动化单边LP策略系统                        ║
║                                                              ║
║  Framework: Agno | Chains: BSC & Solana                     ║
║  Protocols: PancakeSwap v3 & Meteora DLMM v2                ║
╚══════════════════════════════════════════════════════════════╝
        """)
    
    async def run_core_system(self, config_path: str = None):
        """运行核心系统"""
        try:
            # 延迟导入以避免初始化错误
            try:
                from src.utils.helpers import setup_logging
                setup_logging()
            except ImportError:
                print("⚠️ 无法导入日志设置，使用基本日志")

            config_file = config_path or str(self.config_path)
            print(f"🚀 启动DyFlow核心系统，配置文件: {config_file}")

            # 导入并运行主系统
            try:
                from dyflow_main import DyFlowSystem

                system = DyFlowSystem(config_file)
                await system.start()

                # 保持运行
                try:
                    while system.is_running:
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    print("收到中断信号")
                finally:
                    await system.stop()

            except ImportError as e:
                print(f"❌ 无法导入核心系统: {e}")
                print("请检查依赖是否正确安装")
                raise

        except Exception as e:
            print(f"❌ 核心系统运行失败: {e}")
            raise
    
    def run_web_ui(self, mode: str = "auto"):
        """运行Web UI"""
        try:
            print(f"🌐 启动Web UI (模式: {mode})")
            
            # 检查React UI
            react_ui_path = self.project_root / "react-ui"
            web_ui_path = self.project_root / "web_ui"
            
            if mode == "react" or (mode == "auto" and (react_ui_path / "dist").exists()):
                print("✅ 启动React UI")
                app_file = web_ui_path / "react_app.py"
                if app_file.exists():
                    subprocess.run([sys.executable, str(app_file)], cwd=str(self.project_root))
                else:
                    print("❌ React UI后端文件不存在")
                    return 1
                    
            elif mode == "simple" or mode == "auto":
                print("✅ 启动简单Web UI")
                app_file = web_ui_path / "simple_app.py"
                if app_file.exists():
                    subprocess.run([sys.executable, str(app_file)], cwd=str(self.project_root))
                else:
                    print("❌ 简单Web UI文件不存在")
                    return 1
            else:
                print(f"❌ 未知的UI模式: {mode}")
                return 1
                
            return 0
            
        except Exception as e:
            print(f"❌ Web UI启动失败: {e}")
            return 1
    
    def run_backend_only(self):
        """仅运行后端服务"""
        try:
            print("🔧 启动后端服务")
            backend_file = self.project_root / "dyflow_real_data_backend.py"
            
            if backend_file.exists():
                subprocess.run([sys.executable, str(backend_file)], cwd=str(self.project_root))
                return 0
            else:
                print("❌ 后端文件不存在")
                return 1
                
        except Exception as e:
            print(f"❌ 后端启动失败: {e}")
            return 1
    
    async def run_test_mode(self, test_type: str = "basic"):
        """运行测试模式"""
        try:
            print(f"🧪 运行测试模式: {test_type}")
            
            test_files = {
                "basic": "test_basic_components.py",
                "agents": "test_all_agents.py", 
                "trading": "test_trading_executor.py",
                "complete": "test_complete_system.py",
                "real_api": "test_real_api_integration.py"
            }
            
            test_file = test_files.get(test_type)
            if not test_file:
                print(f"❌ 未知的测试类型: {test_type}")
                print(f"可用的测试类型: {', '.join(test_files.keys())}")
                return 1
            
            test_path = self.project_root / test_file
            if test_path.exists():
                result = subprocess.run([sys.executable, str(test_path)], cwd=str(self.project_root))
                return result.returncode
            else:
                print(f"❌ 测试文件不存在: {test_file}")
                return 1
                
        except Exception as e:
            print(f"❌ 测试运行失败: {e}")
            return 1
    
    def show_status(self):
        """显示系统状态"""
        print("📊 DyFlow 系统状态")
        print("=" * 50)
        
        # 检查配置文件
        if self.config_path.exists():
            print("✅ 配置文件: 存在")
        else:
            print("❌ 配置文件: 不存在")
        
        # 检查核心组件
        core_files = [
            "src/supervisor.py",
            "src/agents/trading_executor_agent.py",
            "src/tools/jupiter_swap_tool.py",
            "src/tools/meteora_dlmm_tool.py"
        ]
        
        print("\n📁 核心组件:")
        for file_path in core_files:
            full_path = self.project_root / file_path
            status = "✅" if full_path.exists() else "❌"
            print(f"  {status} {file_path}")
        
        # 检查UI组件
        print("\n🌐 UI组件:")
        ui_components = [
            ("React UI", "react-ui/dist"),
            ("Web UI", "web_ui/app.py"),
            ("简单UI", "web_ui/simple_app.py")
        ]
        
        for name, path in ui_components:
            full_path = self.project_root / path
            status = "✅" if full_path.exists() else "❌"
            print(f"  {status} {name}")
        
        # 检查测试文件
        print("\n🧪 测试组件:")
        test_files = [
            "test_basic_components.py",
            "test_trading_executor.py", 
            "test_complete_system.py"
        ]
        
        for test_file in test_files:
            full_path = self.project_root / test_file
            status = "✅" if full_path.exists() else "❌"
            print(f"  {status} {test_file}")
    
    def install_dependencies(self, force: bool = False):
        """安装依赖"""
        print("🔧 开始安装DyFlow依赖...")

        # 基础Python包
        basic_packages = [
            "fastapi>=0.104.0",
            "uvicorn[standard]>=0.24.0",
            "websockets>=12.0",
            "aiohttp>=3.9.0",
            "structlog>=23.2.0",
            "pydantic>=2.5.0",
            "supabase>=2.0.0",
            "pyyaml>=6.0"
        ]

        # Solana包 (可选)
        solana_packages = [
            "solana>=0.30.0",
            "solders>=0.18.0"
        ]

        # Web3包 (可选)
        web3_packages = [
            "web3>=6.12.0"
        ]

        success_count = 0
        total_count = 0

        # 安装基础包
        print("\n📦 安装基础依赖...")
        for package in basic_packages:
            total_count += 1
            if self._install_package(package, force):
                success_count += 1

        # 安装Solana包
        print("\n🌐 安装Solana依赖...")
        for package in solana_packages:
            total_count += 1
            if self._install_package(package, force):
                success_count += 1

        # 安装Web3包
        print("\n🔗 安装Web3依赖...")
        for package in web3_packages:
            total_count += 1
            if self._install_package(package, force):
                success_count += 1

        # 检查Ollama
        print("\n🤖 检查Ollama...")
        if self._check_ollama():
            print("  ✅ Ollama已安装")
        else:
            print("  ⚠️ Ollama未安装，请手动安装: https://ollama.ai/")

        # 总结
        print(f"\n📊 安装完成: {success_count}/{total_count} 个包安装成功")

        if success_count == total_count:
            print("🎉 所有依赖安装成功！")
            print("\n🚀 下一步:")
            print("  1. 配置环境变量 (复制 .env.example 为 .env)")
            print("  2. 运行测试: python dyflow.py test --type trading")
            return 0
        else:
            print("⚠️ 部分依赖安装失败，但系统仍可在降级模式下运行")
            return 1

    def _install_package(self, package: str, force: bool = False) -> bool:
        """安装单个包"""
        try:
            print(f"  📥 安装 {package}...")
            cmd = [sys.executable, "-m", "pip", "install"]
            if force:
                cmd.append("--force-reinstall")
            cmd.append(package)

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

            if result.returncode == 0:
                print(f"  ✅ {package} 安装成功")
                return True
            else:
                print(f"  ❌ {package} 安装失败: {result.stderr.strip()}")
                return False

        except subprocess.TimeoutExpired:
            print(f"  ⏰ {package} 安装超时")
            return False
        except Exception as e:
            print(f"  ❌ {package} 安装异常: {e}")
            return False

    def _check_ollama(self) -> bool:
        """检查Ollama是否安装"""
        try:
            result = subprocess.run(["ollama", "--version"],
                                  capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except:
            return False

    def show_help(self):
        """显示帮助信息"""
        print("""
DyFlow 统一启动器使用说明:

基本命令:
  python dyflow.py core                    # 运行核心系统
  python dyflow.py ui                      # 运行Web UI (自动选择)
  python dyflow.py ui --mode react         # 运行React UI
  python dyflow.py ui --mode simple        # 运行简单UI
  python dyflow.py backend                 # 仅运行后端服务
  python dyflow.py test                    # 运行基本测试
  python dyflow.py test --type complete    # 运行完整测试
  python dyflow.py status                  # 显示系统状态
  python dyflow.py help                    # 显示此帮助
  python dyflow.py install                 # 安装依赖
  python dyflow.py deps                    # 检查和安装依赖

测试类型:
  basic     - 基本组件测试
  agents    - Agent测试
  trading   - 交易执行器测试
  complete  - 完整系统测试
  real_api  - 真实API集成测试

配置:
  --config PATH    # 指定配置文件路径
  --log-level LEVEL # 设置日志级别 (DEBUG, INFO, WARNING, ERROR)
  --force          # 强制重新安装依赖

示例:
  python dyflow.py core --config config/production.yaml
  python dyflow.py test --type trading --log-level DEBUG
  python dyflow.py install --force
        """)

def main():
    """主函数"""
    launcher = DyFlowLauncher()
    launcher.print_banner()
    
    parser = argparse.ArgumentParser(
        description="DyFlow 统一启动器",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # Core命令
    core_parser = subparsers.add_parser('core', help='运行核心系统')
    core_parser.add_argument('--config', help='配置文件路径')
    core_parser.add_argument('--log-level', default='INFO', help='日志级别')
    
    # UI命令
    ui_parser = subparsers.add_parser('ui', help='运行Web UI')
    ui_parser.add_argument('--mode', choices=['auto', 'react', 'simple'], 
                          default='auto', help='UI模式')
    
    # Backend命令
    subparsers.add_parser('backend', help='仅运行后端服务')
    
    # Test命令
    test_parser = subparsers.add_parser('test', help='运行测试')
    test_parser.add_argument('--type', choices=['basic', 'agents', 'trading', 'complete', 'real_api'],
                            default='basic', help='测试类型')
    test_parser.add_argument('--log-level', default='INFO', help='日志级别')
    
    # Status命令
    subparsers.add_parser('status', help='显示系统状态')
    
    # Help命令
    subparsers.add_parser('help', help='显示详细帮助')

    # Install命令
    install_parser = subparsers.add_parser('install', help='安装依赖')
    install_parser.add_argument('--force', action='store_true', help='强制重新安装')

    # Deps命令 (install的别名)
    deps_parser = subparsers.add_parser('deps', help='检查和安装依赖')
    deps_parser.add_argument('--force', action='store_true', help='强制重新安装')

    args = parser.parse_args()
    
    if not args.command:
        launcher.show_help()
        return 0
    
    # 设置日志级别
    if hasattr(args, 'log_level'):
        os.environ['LOG_LEVEL'] = args.log_level
    
    try:
        if args.command == 'core':
            asyncio.run(launcher.run_core_system(args.config))
        elif args.command == 'ui':
            return launcher.run_web_ui(args.mode)
        elif args.command == 'backend':
            return launcher.run_backend_only()
        elif args.command == 'test':
            return asyncio.run(launcher.run_test_mode(args.type))
        elif args.command == 'status':
            launcher.show_status()
        elif args.command == 'help':
            launcher.show_help()
        elif args.command in ['install', 'deps']:
            return launcher.install_dependencies(getattr(args, 'force', False))
        else:
            print(f"未知命令: {args.command}")
            launcher.show_help()
            return 1
            
        return 0
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        return 0
    except Exception as e:
        print(f"程序运行失败: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
