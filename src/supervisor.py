"""
DyFlow Supervisor - 基於Agno Framework的統一調度器
實現 Supervisor → Agent → Tool → Workflow 架構
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import structlog
from dataclasses import dataclass
import yaml
import os

# Agno Framework imports
try:
    from agno.supervisor import Supervisor
    from agno.agent import Agent
    from agno.workflow import Workflow
    AGNO_AVAILABLE = True
except ImportError:
    AGNO_AVAILABLE = False
    # Fallback classes
    class Supervisor:
        pass
    class Agent:
        pass
    class Workflow:
        pass

# DyFlow imports
from .agents.planner_agno import PlannerAgnoAgent
from .agents.risk_sentinel_agno import RiskSentinelAgnoAgent
from .agents.portfolio_agent import PortfolioAgent
from .agents.hedge_agent import HedgeAgent
from .agents.range_rebalancer_agent import RangeRebalancerAgent
from .agents.data_provider_agent import DataProviderAgent  # 新增数据提供代理
from .workflows.lp_monitoring_workflow import LPMonitoringWorkflow
from .utils.config import Config
from .utils.helpers import get_utc_timestamp
from .utils.exceptions import DyFlowException

logger = structlog.get_logger(__name__)

@dataclass
class SupervisorConfig:
    """Supervisor配置"""
    enable_agno: bool = True
    enable_scheduling: bool = True
    enable_monitoring: bool = True
    max_concurrent_agents: int = 5
    agent_timeout_seconds: int = 300
    workflow_timeout_seconds: int = 600
    error_retry_attempts: int = 3
    error_retry_delay_seconds: int = 30

@dataclass
class AgentExecutionResult:
    """Agent執行結果"""
    agent_name: str
    status: str  # success, error, timeout
    data: Any
    execution_time: float
    timestamp: datetime
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class DyFlowSupervisor:
    """DyFlow主要調度器 - 實現PRD中的Supervisor角色"""
    
    def __init__(self, config: Config, supervisor_config: SupervisorConfig = None):
        self.config = config
        self.supervisor_config = supervisor_config or SupervisorConfig()
        self.logger = structlog.get_logger("dyflow_supervisor")
        
        # Agent註冊表
        self.agents: Dict[str, Any] = {}
        self.workflows: Dict[str, Any] = {}
        
        # 執行狀態
        self.is_running = False
        self.execution_history: List[AgentExecutionResult] = []
        self.last_execution_times: Dict[str, datetime] = {}
        
        # 調度配置
        self.schedule_config = {}
        
        # Agno Supervisor實例
        self.agno_supervisor: Optional[Supervisor] = None
        
    async def initialize(self) -> None:
        """初始化Supervisor和所有Agents"""
        try:
            self.logger.info("dyflow_supervisor_initializing")
            
            # 初始化Agno Supervisor
            if AGNO_AVAILABLE and self.supervisor_config.enable_agno:
                self.agno_supervisor = Supervisor(
                    name="DyFlowSupervisor",
                    description="DyFlow低流通Meme幣LP策略系統主調度器"
                )
            
            # 註冊核心Agents (按PRD順序)
            await self._register_agents()
            
            # 註冊Workflows
            await self._register_workflows()
            
            # 載入調度配置
            await self._load_schedule_config()
            
            self.logger.info("dyflow_supervisor_initialized",
                           agents_count=len(self.agents),
                           workflows_count=len(self.workflows),
                           agno_enabled=self.agno_supervisor is not None)
            
        except Exception as e:
            self.logger.error("dyflow_supervisor_initialization_failed", error=str(e))
            raise DyFlowException(f"Supervisor初始化失敗: {e}")
    
    async def _register_agents(self) -> None:
        """註冊所有Agents - 按PRD定義的角色"""
        agent_configs = {
            # 0. DataProvider - 数据提供和管理 (最高优先级)
            'data_provider': {
                'class': DataProviderAgent,
                'config': self.config.get_agent_config('data_provider', {}),
                'description': '实时数据提供和管理Agent',
                'schedule': '*/3 * * * *'  # 每3分钟更新数据
            },

            # 1. PoolPicker - 池子發現和篩選
            'pool_picker': {
                'class': PlannerAgnoAgent,
                'config': self.config.get_agent_config('planner'),
                'description': '池子發現和篩選Agent',
                'schedule': '*/5 * * * *'  # 每5分鐘
            },
            
            # 2. RangeRebalancer - LP範圍管理和執行
            'range_rebalancer': {
                'class': RangeRebalancerAgent,
                'config': self.config.get_agent_config('range_rebalancer'),
                'description': 'LP範圍管理和執行Agent',
                'schedule': '*/15 * * * *'  # 每15分鐘
            },
            
            # 3. HedgeExecutor - DCA出貨和對沖
            'hedge_executor': {
                'class': HedgeAgent,
                'config': self.config.get_agent_config('hedge'),
                'description': 'DCA出貨和對沖執行Agent',
                'schedule': '*/10 * * * *'  # 每10分鐘
            },
            
            # 4. RiskSentinel - 風險監控和熔斷
            'risk_sentinel': {
                'class': RiskSentinelAgnoAgent,
                'config': self.config.get_agent_config('risk_sentinel'),
                'description': '風險監控和熔斷Agent',
                'schedule': '*/2 * * * *'  # 每2分鐘
            },
            
            # 5. PortfolioManager - 組合管理和報告
            'portfolio_manager': {
                'class': PortfolioAgent,
                'config': self.config.get_agent_config('portfolio'),
                'description': '組合管理和報告Agent',
                'schedule': '*/15 * * * *'  # 每15分鐘
            }
        }
        
        for agent_name, agent_info in agent_configs.items():
            try:
                # 創建Agent實例
                agent_class = agent_info['class']
                agent_config = agent_info['config']

                # 根據不同的Agent類型使用不同的構造參數
                if agent_class in [RangeRebalancerAgent, HedgeAgent, PortfolioAgent]:
                    # 這些Agent需要 name, config, database 參數，但我們修改為使用BaseAgent接口
                    agent_instance = agent_class(agent_name, agent_config)
                elif agent_class == DataProviderAgent:
                    # DataProviderAgent 只需要 model 參數
                    agent_instance = agent_class()
                else:
                    # 其他Agent (如 PlannerAgnoAgent, RiskSentinelAgnoAgent) 只需要 config
                    agent_instance = agent_class(agent_config)
                await agent_instance.initialize()
                
                # 註冊到Supervisor
                self.agents[agent_name] = {
                    'instance': agent_instance,
                    'config': agent_info,
                    'last_execution': None,
                    'execution_count': 0,
                    'error_count': 0
                }
                
                # 設置調度
                self.schedule_config[agent_name] = agent_info['schedule']
                
                self.logger.info("agent_registered",
                               agent_name=agent_name,
                               schedule=agent_info['schedule'])
                
            except Exception as e:
                self.logger.error("agent_registration_failed",
                                agent_name=agent_name,
                                error=str(e))
    
    async def _register_workflows(self) -> None:
        """註冊Workflows"""
        try:
            # LP監控工作流程
            lp_monitoring_workflow = LPMonitoringWorkflow()
            
            self.workflows['lp_monitoring'] = {
                'instance': lp_monitoring_workflow,
                'description': 'LP池子監控和分析工作流程',
                'schedule': '*/30 * * * *'  # 每30分鐘
            }
            
            self.logger.info("workflows_registered", count=len(self.workflows))
            
        except Exception as e:
            self.logger.error("workflow_registration_failed", error=str(e))
    
    async def _load_schedule_config(self) -> None:
        """載入調度配置"""
        try:
            # 嘗試從YAML文件載入
            config_path = "workflows/low-float.yaml"
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    yaml_config = yaml.safe_load(f)
                    
                # 更新調度配置
                if 'steps' in yaml_config:
                    for step in yaml_config['steps']:
                        step_id = step.get('id')
                        schedule = step.get('schedule')
                        if step_id and schedule:
                            self.schedule_config[step_id] = schedule
                            
                self.logger.info("schedule_config_loaded", config_path=config_path)
            else:
                self.logger.info("using_default_schedule_config")
                
        except Exception as e:
            self.logger.warning("schedule_config_load_failed", error=str(e))
    
    async def start(self) -> None:
        """啟動Supervisor"""
        if self.is_running:
            self.logger.warning("supervisor_already_running")
            return
            
        try:
            self.is_running = True
            self.logger.info("dyflow_supervisor_starting")
            
            # 啟動主執行循環
            await self._main_execution_loop()
            
        except Exception as e:
            self.logger.error("supervisor_start_failed", error=str(e))
            self.is_running = False
            raise
    
    async def stop(self) -> None:
        """停止Supervisor"""
        self.is_running = False
        self.logger.info("dyflow_supervisor_stopping")
        
        # 等待當前執行完成
        await asyncio.sleep(2)
        
        # 清理資源
        for agent_name, agent_info in self.agents.items():
            try:
                if hasattr(agent_info['instance'], 'cleanup'):
                    await agent_info['instance'].cleanup()
            except Exception as e:
                self.logger.error("agent_cleanup_failed", 
                                agent_name=agent_name, 
                                error=str(e))
        
        self.logger.info("dyflow_supervisor_stopped")
    
    async def _main_execution_loop(self) -> None:
        """主執行循環"""
        while self.is_running:
            try:
                # 檢查需要執行的Agents
                agents_to_execute = self._get_agents_to_execute()
                
                if agents_to_execute:
                    # 並發執行Agents (限制並發數)
                    await self._execute_agents_batch(agents_to_execute)
                
                # 檢查需要執行的Workflows
                workflows_to_execute = self._get_workflows_to_execute()
                
                if workflows_to_execute:
                    await self._execute_workflows_batch(workflows_to_execute)
                
                # 清理執行歷史
                self._cleanup_execution_history()
                
                # 等待下一個執行週期
                await asyncio.sleep(30)  # 30秒檢查一次
                
            except Exception as e:
                self.logger.error("main_execution_loop_error", error=str(e))
                await asyncio.sleep(60)  # 錯誤時等待更長時間
    
    def _get_agents_to_execute(self) -> List[str]:
        """獲取需要執行的Agents"""
        agents_to_execute = []
        current_time = datetime.now()
        
        for agent_name, agent_info in self.agents.items():
            last_execution = agent_info.get('last_execution')
            schedule = self.schedule_config.get(agent_name)
            
            if self._should_execute(agent_name, last_execution, schedule, current_time):
                agents_to_execute.append(agent_name)
        
        return agents_to_execute
    
    def _get_workflows_to_execute(self) -> List[str]:
        """獲取需要執行的Workflows"""
        workflows_to_execute = []
        current_time = datetime.now()
        
        for workflow_name, workflow_info in self.workflows.items():
            schedule = workflow_info.get('schedule', '*/30 * * * *')
            last_execution = workflow_info.get('last_execution')
            
            if self._should_execute(workflow_name, last_execution, schedule, current_time):
                workflows_to_execute.append(workflow_name)
        
        return workflows_to_execute
    
    def _should_execute(self, name: str, last_execution: Optional[datetime], 
                       schedule: str, current_time: datetime) -> bool:
        """判斷是否應該執行"""
        if not last_execution:
            return True
            
        # 簡化的調度邏輯 - 基於分鐘間隔
        if '*/2 *' in schedule:  # 每2分鐘
            return current_time - last_execution >= timedelta(minutes=2)
        elif '*/5 *' in schedule:  # 每5分鐘
            return current_time - last_execution >= timedelta(minutes=5)
        elif '*/10 *' in schedule:  # 每10分鐘
            return current_time - last_execution >= timedelta(minutes=10)
        elif '*/15 *' in schedule:  # 每15分鐘
            return current_time - last_execution >= timedelta(minutes=15)
        elif '*/30 *' in schedule:  # 每30分鐘
            return current_time - last_execution >= timedelta(minutes=30)
        else:
            return current_time - last_execution >= timedelta(minutes=5)  # 默認5分鐘

    async def _execute_agents_batch(self, agent_names: List[str]) -> None:
        """批量執行Agents"""
        max_concurrent = self.supervisor_config.max_concurrent_agents

        # 分批執行以控制並發數
        for i in range(0, len(agent_names), max_concurrent):
            batch = agent_names[i:i + max_concurrent]

            # 創建執行任務
            tasks = []
            for agent_name in batch:
                task = asyncio.create_task(self._execute_single_agent(agent_name))
                tasks.append(task)

            # 等待批次完成
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 處理結果
            for agent_name, result in zip(batch, results):
                if isinstance(result, Exception):
                    self.logger.error("agent_execution_exception",
                                    agent_name=agent_name,
                                    error=str(result))
                else:
                    self.execution_history.append(result)

    async def _execute_single_agent(self, agent_name: str) -> AgentExecutionResult:
        """執行單個Agent"""
        start_time = datetime.now()
        agent_info = self.agents[agent_name]
        agent_instance = agent_info['instance']

        try:
            self.logger.info("agent_execution_started", agent_name=agent_name)

            # 執行Agent (帶超時)
            result = await asyncio.wait_for(
                agent_instance.execute(),
                timeout=self.supervisor_config.agent_timeout_seconds
            )

            execution_time = (datetime.now() - start_time).total_seconds()

            # 更新執行記錄
            agent_info['last_execution'] = datetime.now()
            agent_info['execution_count'] += 1

            self.logger.info("agent_execution_completed",
                           agent_name=agent_name,
                           execution_time=execution_time,
                           status=result.status)

            return AgentExecutionResult(
                agent_name=agent_name,
                status=result.status,
                data=result.data,
                execution_time=execution_time,
                timestamp=datetime.now(),
                metadata=result.metadata
            )

        except asyncio.TimeoutError:
            execution_time = (datetime.now() - start_time).total_seconds()
            agent_info['error_count'] += 1

            self.logger.error("agent_execution_timeout",
                            agent_name=agent_name,
                            timeout_seconds=self.supervisor_config.agent_timeout_seconds)

            return AgentExecutionResult(
                agent_name=agent_name,
                status="timeout",
                data=None,
                execution_time=execution_time,
                timestamp=datetime.now(),
                error_message=f"Agent執行超時 ({self.supervisor_config.agent_timeout_seconds}s)"
            )

        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            agent_info['error_count'] += 1

            self.logger.error("agent_execution_failed",
                            agent_name=agent_name,
                            error=str(e))

            return AgentExecutionResult(
                agent_name=agent_name,
                status="error",
                data=None,
                execution_time=execution_time,
                timestamp=datetime.now(),
                error_message=str(e)
            )

    async def _execute_workflows_batch(self, workflow_names: List[str]) -> None:
        """批量執行Workflows"""
        for workflow_name in workflow_names:
            try:
                await self._execute_single_workflow(workflow_name)
            except Exception as e:
                self.logger.error("workflow_execution_failed",
                                workflow_name=workflow_name,
                                error=str(e))

    async def _execute_single_workflow(self, workflow_name: str) -> None:
        """執行單個Workflow"""
        workflow_info = self.workflows[workflow_name]
        workflow_instance = workflow_info['instance']

        try:
            self.logger.info("workflow_execution_started", workflow_name=workflow_name)

            # 執行Workflow
            result = workflow_instance.monitor_pools()

            # 更新執行記錄
            workflow_info['last_execution'] = datetime.now()

            self.logger.info("workflow_execution_completed",
                           workflow_name=workflow_name,
                           status=result.get('status', 'unknown'))

        except Exception as e:
            self.logger.error("workflow_execution_error",
                            workflow_name=workflow_name,
                            error=str(e))

    def _cleanup_execution_history(self) -> None:
        """清理執行歷史"""
        # 只保留最近1000條記錄
        if len(self.execution_history) > 1000:
            self.execution_history = self.execution_history[-1000:]

    async def execute_agent_manually(self, agent_name: str) -> AgentExecutionResult:
        """手動執行指定Agent"""
        if agent_name not in self.agents:
            raise DyFlowException(f"Agent不存在: {agent_name}")

        return await self._execute_single_agent(agent_name)

    async def execute_workflow_manually(self, workflow_name: str) -> Dict[str, Any]:
        """手動執行指定Workflow"""
        if workflow_name not in self.workflows:
            raise DyFlowException(f"Workflow不存在: {workflow_name}")

        workflow_info = self.workflows[workflow_name]
        workflow_instance = workflow_info['instance']

        return workflow_instance.monitor_pools()

    def get_system_status(self) -> Dict[str, Any]:
        """獲取系統狀態"""
        return {
            'supervisor': {
                'is_running': self.is_running,
                'agno_enabled': self.agno_supervisor is not None,
                'uptime_seconds': (datetime.now() - self.start_time).total_seconds() if hasattr(self, 'start_time') else 0
            },
            'agents': {
                agent_name: {
                    'execution_count': agent_info['execution_count'],
                    'error_count': agent_info['error_count'],
                    'last_execution': agent_info['last_execution'].isoformat() if agent_info['last_execution'] else None,
                    'schedule': self.schedule_config.get(agent_name)
                }
                for agent_name, agent_info in self.agents.items()
            },
            'workflows': {
                workflow_name: {
                    'last_execution': workflow_info.get('last_execution').isoformat() if workflow_info.get('last_execution') else None,
                    'schedule': workflow_info.get('schedule')
                }
                for workflow_name, workflow_info in self.workflows.items()
            },
            'execution_history_count': len(self.execution_history),
            'recent_errors': [
                {
                    'agent_name': result.agent_name,
                    'error_message': result.error_message,
                    'timestamp': result.timestamp.isoformat()
                }
                for result in self.execution_history[-10:]
                if result.status in ['error', 'timeout']
            ]
        }
